# Train Ticketing System

A web-based train ticket booking system built with Node.js, Express, MongoDB, and EJS templating engine.

## Features

- **Ticket Booking**: Book train tickets with passenger details
- **PNR Generation**: Automatic generation of unique PNR (Passenger Name Record) numbers
- **Class Selection**: Multiple travel classes available:
  - First AC (1A)
  - Second AC (2A) 
  - Third AC (3A)
  - Sleeper (SL)
  - Chair Car (CC)
- **Passenger Information**: Capture essential passenger details including name, age, gender, mobile number, and email
- **Route Selection**: From and To station selection
- **Date Selection**: Travel date booking
- **PNR Display**: View generated PNR after successful booking

## Tech Stack

- **Backend**: Node.js with Express.js framework
- **Database**: MongoDB with Mongoose ODM
- **Frontend**: EJS templating engine
- **Styling**: Custom CSS
- **Development**: Nodemon for auto-restart during development

## Prerequisites

Before running this application, make sure you have the following installed:

- [Node.js](https://nodejs.org/) (v14 or higher)
- [MongoDB](https://www.mongodb.com/try/download/community) (running locally on port 27017)
- npm (comes with Node.js)

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd train-ticketing-system
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start MongoDB**
   Make sure MongoDB is running on your local machine at `mongodb://127.0.0.1:27017`

4. **Run the application**
   ```bash
   npm start
   ```

5. **Access the application**
   Open your browser and navigate to `http://localhost:5000`

## Project Structure

```
train-ticketing-system/
├── index.js              # Main server file
├── package.json          # Project dependencies and scripts
├── package-lock.json     # Locked dependency versions
├── public/
│   └── style.css         # CSS styling
├── views/
│   ├── index.ejs         # Main booking form page
│   └── pnr.ejs          # PNR display page
└── node_modules/         # Dependencies (auto-generated)
```

## API Endpoints

### GET Routes
- `GET /` - Main booking form page
- `GET /pnr` - PNR display page

### POST Routes
- `POST /submit-booking` - Submit booking form and generate PNR

## Database Schema

The application uses a MongoDB collection with the following schema:

```javascript
{
  PNR: String (unique),           // Auto-generated PNR
  From: String (required),        // Departure station
  To: String (required),          // Destination station
  Date: Date (default: now),      // Travel date
  Class: String (required),       // Travel class
  Name: String (required),        // Passenger name
  Age: Number,                    // Passenger age
  gender: String (required),      // Male/Female/Other
  mobilenumber: String (required), // Contact number
  email: String (required, unique) // Email address
}
```

## Usage

1. **Book a Ticket**:
   - Navigate to the home page (`/`)
   - Fill in all required fields in the booking form
   - Select departure and destination stations
   - Choose travel date and class
   - Enter passenger details
   - Submit the form

2. **View PNR**:
   - After successful booking, you'll be redirected to the PNR page
   - The generated PNR will be displayed

## PNR Format

PNRs are automatically generated in the format: `[2 Random Letters][6 Random Numbers]`
Example: `AB123456`

## Development

To run the application in development mode with auto-restart:

```bash
npm start
```

This uses nodemon to automatically restart the server when files change.

## Configuration

- **Port**: The application runs on port 5000 by default
- **Database**: MongoDB connection string is set to `mongodb://127.0.0.1:27017/mongodb-2`
- **Database Name**: `mongodb-2`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

ISC License

## Future Enhancements

- Add PNR search functionality
- Implement ticket cancellation
- Add payment integration
- Include seat selection
- Add train schedule management
- Implement user authentication
- Add booking history
- Include email notifications
